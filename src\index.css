@tailwind base;
@tailwind components;
@tailwind utilities;

/* Courtroom Party Game Design System - Rich mahogany, cream, and gold palette */

@layer base {
  :root {
    /* Base colors - Coffee-stained paper theme */
    --background: 35 25% 85%;         /* Coffee-stained paper */
    --foreground: 25 15% 15%;         /* Dark brown text */

    /* Card styling */
    --card: 35 20% 90%;               /* Light coffee paper */
    --card-foreground: 25 15% 15%;    /* Dark brown text */

    /* Popover styling */
    --popover: 35 20% 90%;
    --popover-foreground: 25 15% 15%;

    /* Primary - Gold accents for justice theme */
    --primary: 45 85% 45%;            /* Rich gold */
    --primary-foreground: 35 20% 90%; /* Light paper on gold */

    /* Secondary - Coffee brown tones */
    --secondary: 25 25% 75%;          /* Light coffee brown */
    --secondary-foreground: 25 15% 15%;

    /* Muted elements */
    --muted: 25 15% 70%;              /* Light muted brown */
    --muted-foreground: 25 20% 40%;   /* Medium brown text */

    /* Accent - Amber highlights */
    --accent: 38 75% 45%;             /* Amber accent */
    --accent-foreground: 30 25% 12%;

    /* Destructive - Deep red for errors */
    --destructive: 0 65% 45%;
    --destructive-foreground: 45 25% 92%;

    /* Borders and inputs */
    --border: 25 20% 60%;             /* Medium coffee border */
    --input: 35 20% 88%;              /* Light coffee input background */
    --ring: 45 85% 55%;               /* Gold focus ring */

    /* Design system extensions */
    --radius: 0.75rem;
    
    /* Justice theme colors */
    --justice-gold: 45 85% 55%;       /* Primary gold */
    --justice-bronze: 25 45% 35%;     /* Bronze accents */
    --mahogany: 20 35% 20%;           /* Rich mahogany */
    --parchment: 45 30% 88%;          /* Parchment cream */
    
    /* Gradients */
    --gradient-justice: linear-gradient(135deg, hsl(var(--justice-gold)), hsl(var(--justice-bronze)));
    --gradient-card: linear-gradient(145deg, hsl(var(--card)), hsl(30 25% 8%));
    --gradient-glow: radial-gradient(circle, hsl(var(--justice-gold) / 0.3), transparent 70%);
    
    /* Shadows */
    --shadow-card: 0 8px 32px hsl(0 0% 0% / 0.4);
    --shadow-glow: 0 0 30px hsl(var(--justice-gold) / 0.5);
    --shadow-elegant: 0 4px 20px hsl(0 0% 0% / 0.3);
    
    /* Animations */
    --transition-smooth: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-card: transform 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    --transition-glow: box-shadow 0.3s ease-in-out;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  /* Dark mode uses same rich courtroom palette */
  .dark {
    --background: 35 15% 8%;
    --foreground: 45 25% 92%;
    --card: 30 25% 12%;
    --card-foreground: 45 25% 92%;
    --popover: 30 25% 12%;
    --popover-foreground: 45 25% 92%;
    --primary: 45 85% 55%;
    --primary-foreground: 30 25% 12%;
    --secondary: 25 35% 25%;
    --secondary-foreground: 45 25% 92%;
    --muted: 30 20% 18%;
    --muted-foreground: 35 15% 65%;
    --accent: 38 75% 45%;
    --accent-foreground: 30 25% 12%;
    --destructive: 0 65% 45%;
    --destructive-foreground: 45 25% 92%;
    --border: 30 25% 22%;
    --input: 30 25% 15%;
    --ring: 45 85% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-serif;
    background:
      radial-gradient(circle at 20% 30%, rgba(139, 69, 19, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 70%, rgba(160, 82, 45, 0.08) 0%, transparent 50%),
      radial-gradient(circle at 40% 80%, rgba(101, 67, 33, 0.06) 0%, transparent 50%),
      linear-gradient(135deg, hsl(var(--background)), hsl(35 30% 82%));
    min-height: 100vh;
  }
  
  /* Custom courtroom styling */
  .justice-border {
    background: var(--gradient-justice);
    padding: 2px;
    border-radius: calc(var(--radius) + 2px);
  }
  
  .card-ornate {
    background: hsl(var(--card));
    border: 2px solid hsl(var(--justice-bronze));
    box-shadow: 0 4px 20px rgba(101, 67, 33, 0.2);
    position: relative;
  }

  .card-ornate::before {
    content: '';
    position: absolute;
    inset: -2px;
    background: linear-gradient(135deg, hsl(var(--justice-gold)), hsl(var(--justice-bronze)));
    border-radius: inherit;
    z-index: -1;
    opacity: 0.4;
  }
  
  .glow-effect {
    animation: glow-pulse 2s ease-in-out infinite alternate;
  }
  
  @keyframes glow-pulse {
    from {
      box-shadow: 0 0 20px hsl(var(--justice-gold) / 0.4);
    }
    to {
      box-shadow: 0 0 40px hsl(var(--justice-gold) / 0.7), 0 0 60px hsl(var(--justice-gold) / 0.3);
    }
  }
  
  .card-flip {
    perspective: 1200px;
  }

  .card-flip-inner {
    transition: transform 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    transform-style: preserve-3d;
    position: relative;
  }

  .card-flip-inner.flipped {
    transform: rotateY(180deg);
  }

  .card-face {
    backface-visibility: hidden;
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    background: hsl(var(--card));
    opacity: 1;
  }

  .card-back {
    transform: rotateY(180deg);
  }

  /* Enhanced card reveal animation */
  .card-reveal-animation {
    animation: cardReveal 0.8s ease-out forwards;
  }

  @keyframes cardReveal {
    0% {
      transform: scale(0.9) rotateY(0deg);
      opacity: 0.8;
    }
    50% {
      transform: scale(1.05) rotateY(90deg);
      opacity: 0.9;
    }
    100% {
      transform: scale(1) rotateY(180deg);
      opacity: 1;
    }
  }

  /* Card image animation */
  .card-image-reveal {
    animation: imageReveal 0.6s ease-out 0.4s both;
  }

  @keyframes imageReveal {
    0% {
      opacity: 0;
      transform: scale(0.8) translateY(20px);
    }
    100% {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
  }

  /* Card glow effect for revealed cards */
  .card-revealed-glow {
    box-shadow: 0 0 30px hsl(var(--justice-gold) / 0.4),
                0 0 60px hsl(var(--justice-gold) / 0.2),
                0 8px 32px rgba(0, 0, 0, 0.3);
    animation: revealGlow 2s ease-in-out infinite alternate;
  }

  @keyframes revealGlow {
    0% {
      box-shadow: 0 0 20px hsl(var(--justice-gold) / 0.3),
                  0 0 40px hsl(var(--justice-gold) / 0.1),
                  0 8px 32px rgba(0, 0, 0, 0.3);
    }
    100% {
      box-shadow: 0 0 40px hsl(var(--justice-gold) / 0.5),
                  0 0 80px hsl(var(--justice-gold) / 0.3),
                  0 8px 32px rgba(0, 0, 0, 0.3);
    }
  }
}
