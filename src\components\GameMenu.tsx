import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Slider } from "@/components/ui/slider";
import { Scale, Gavel, Users, Play } from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface GameMenuProps {
  onStartGame: (config: GameConfig) => void;
}

export interface GameConfig {
  playerCount: number;
  secretWord: string;
  liarCount: number;
  manipulatorMode: boolean;
}

export function GameMenu({ onStartGame }: GameMenuProps) {
  const [playerCount, setPlayerCount] = useState([6]);
  const [secretWord, setSecretWord] = useState("");
  const [liarCount, setLiarCount] = useState([1]);
  const [manipulatorMode, setManipulatorMode] = useState(false);

  const predefinedWords = [
    "AV DICEY", "NATURAL JUSTICE", "PARLIAMENTARY SOVEREIGNTY",
    "DELEGATED LEGISLATION", "IN RE DELHI LAWS ACT", "DIVINE ORIGIN THEORY",
    "DROIT ADMINISTRATIF", "ADMINISTRATIVE TRIBUNALS", "QUASI JUDICIAL FUNCTIONS",
    "DISCRETION", "NEMO JUDEX IN CAUSA SUA"
  ];

  const handleStartTrial = () => {
    if (!secretWord.trim()) {
      alert("Please enter a secret word to begin the trial!");
      return;
    }
    
    onStartGame({
      playerCount: playerCount[0],
      secretWord: secretWord.trim(),
      liarCount: liarCount[0],
      manipulatorMode,
    });
  };

  return (
    <div className="min-h-screen p-6 flex items-center justify-center">
      <div className="w-full max-w-2xl space-y-8 animate-fade-in-up">
        {/* Header */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center gap-3 mb-6">
            <Scale className="h-12 w-12 text-justice-gold animate-gavel-strike" />
            <h1 className="text-4xl font-bold bg-gradient-to-r from-justice-gold to-justice-bronze bg-clip-text text-transparent">
              Courtroom Trial
            </h1>
            <Scale className="h-12 w-12 text-justice-gold animate-gavel-strike [animation-delay:0.3s]" />
          </div>
          <p className="text-lg text-muted-foreground">
            A Party Game of Truth, Deception, and Justice
          </p>
        </div>

        {/* Main Game Setup Card */}
        <Card className="card-ornate">
          <CardHeader className="text-center">
            <CardTitle className="flex items-center justify-center gap-2 text-2xl">
              <Gavel className="h-6 w-6 text-justice-gold" />
              Trial Setup
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Player Count */}
            <div className="space-y-3">
              <Label className="text-base font-semibold flex items-center gap-2">
                <Users className="h-4 w-4 text-justice-gold" />
                Number of Players: {playerCount[0]}
              </Label>
              <Slider
                value={playerCount}
                onValueChange={setPlayerCount}
                min={3}
                max={15}
                step={1}
                className="w-full"
              />
              <div className="flex justify-between text-sm text-muted-foreground">
                <span>3 players</span>
                <span>15 players</span>
              </div>
            </div>

            {/* Secret Word */}
            <div className="space-y-3">
              <Label className="text-base font-semibold">
                Secret Word
              </Label>
              <Select value={secretWord} onValueChange={setSecretWord}>
                <SelectTrigger className="text-center text-lg font-medium">
                  <SelectValue placeholder="Choose a word for truth-tellers..." />
                </SelectTrigger>
                <SelectContent>
                  {predefinedWords.map((word) => (
                    <SelectItem key={word} value={word} className="text-center">
                      {word}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Liar Count */}
            <div className="space-y-3">
              <Label className="text-base font-semibold">
                Number of Liars: {liarCount[0]}
              </Label>
              <Slider
                value={liarCount}
                onValueChange={setLiarCount}
                min={1}
                max={Math.max(1, Math.floor(playerCount[0] / 2))}
                step={1}
                className="w-full"
              />
              <div className="flex justify-between text-sm text-muted-foreground">
                <span>1 liar</span>
                <span>{Math.max(1, Math.floor(playerCount[0] / 2))} liars</span>
              </div>
            </div>

            {/* Manipulator Mode */}
            <div className="flex items-center justify-between p-4 bg-muted/30 rounded-lg">
              <div>
                <Label className="text-base font-semibold">Manipulator Mode</Label>
                <p className="text-sm text-muted-foreground">
                  One player can see everyone's cards
                </p>
              </div>
              <Switch
                checked={manipulatorMode}
                onCheckedChange={setManipulatorMode}
              />
            </div>
          </CardContent>
        </Card>


        {/* Start Trial Button */}
        <div className="text-center">
          <Button
            variant="justice"
            size="xl"
            onClick={handleStartTrial}
            disabled={!secretWord.trim()}
            className={`w-full max-w-md transition-all duration-300 ${
              secretWord.trim() 
                ? "animate-justice-glow" 
                : "opacity-50 cursor-not-allowed"
            }`}
          >
            <Play className="h-6 w-6 mr-2" />
            Start Trial
          </Button>
        </div>
      </div>
    </div>
  );
}