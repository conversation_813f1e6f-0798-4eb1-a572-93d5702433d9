import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Scale, ArrowRight, RotateCcw, Home } from "lucide-react";
import { GameConfig } from "./GameMenu";
import liarCardImage from "@/assets/liar-card.jpg";
import manipulatorCardImage from "@/assets/manipulator-card.jpg";

// Import all legal term card images
import avDiceyCard from "@/assets/av-dicey-card.jpg";
import naturalJusticeCard from "@/assets/natural-justice-card.jpg";
import parliamentarySovereigntyCard from "@/assets/parliamitaray-soverenity-card.jpg";
import delegatedLegislationCard from "@/assets/delegated-legislation-card.jpg";
import inReDelhiLawsActCard from "@/assets/inre-delhi-law-act-case-card.jpg";
import divineOriginTheoryCard from "@/assets/divine-origin-theory-card.jpg";
import droitAdministratifCard from "@/assets/droit-administratif-card.jpg";
import administrativeTribunalsCard from "@/assets/tribiunals-card.jpg";
import quasiJudicialFunctionsCard from "@/assets/admininitrative-law-card.jpg";
import discretionCard from "@/assets/discretion-card.jpg";
import nemoJudexCard from "@/assets/nemo-judex-in-causa-sua-card.jpg";

interface GamePlayProps {
  config: GameConfig;
  onBackToMenu: () => void;
}

interface PlayerCard {
  id: number;
  isLiar: boolean;
  hasSeenCard: boolean;
}

// Mapping of legal terms to their card images
const cardImageMap: Record<string, string> = {
  "AV DICEY": avDiceyCard,
  "NATURAL JUSTICE": naturalJusticeCard,
  "PARLIAMENTARY SOVEREIGNTY": parliamentarySovereigntyCard,
  "DELEGATED LEGISLATION": delegatedLegislationCard,
  "IN RE DELHI LAWS ACT": inReDelhiLawsActCard,
  "DIVINE ORIGIN THEORY": divineOriginTheoryCard,
  "DROIT ADMINISTRATIF": droitAdministratifCard,
  "ADMINISTRATIVE TRIBUNALS": administrativeTribunalsCard,
  "QUASI JUDICIAL FUNCTIONS": quasiJudicialFunctionsCard,
  "DISCRETION": discretionCard,
  "NEMO JUDEX IN CAUSA SUA": nemoJudexCard,
};

export function GamePlay({ config, onBackToMenu }: GamePlayProps) {
  const [players, setPlayers] = useState<PlayerCard[]>([]);
  const [currentPlayer, setCurrentPlayer] = useState(0);
  const [isFlipped, setIsFlipped] = useState(false);
  const [timer, setTimer] = useState(10);
  const [gamePhase, setGamePhase] = useState<"setup" | "playing" | "ended">("setup");
  const [showRole, setShowRole] = useState(false);

  useEffect(() => {
    // Initialize players
    const newPlayers: PlayerCard[] = [];
    const liarIndices = new Set<number>();
    
    // Randomly select liars
    while (liarIndices.size < config.liarCount) {
      liarIndices.add(Math.floor(Math.random() * config.playerCount));
    }
    
    for (let i = 0; i < config.playerCount; i++) {
      newPlayers.push({
        id: i + 1,
        isLiar: liarIndices.has(i),
        hasSeenCard: false,
      });
    }
    
    setPlayers(newPlayers);
    setGamePhase("playing");
  }, [config]);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (gamePhase === "playing" && showRole && timer > 0) {
      interval = setInterval(() => {
        setTimer((prev) => prev - 1);
      }, 1000);
    } else if (timer === 0 && showRole) {
      handleNextPlayer();
    }
    
    return () => clearInterval(interval);
  }, [gamePhase, showRole, timer]);

  const handleRevealCard = () => {
    setIsFlipped(true);
    setShowRole(true);
    setTimer(15);
    
    // Mark current player as having seen their card
    setPlayers(prev => prev.map((player, index) => 
      index === currentPlayer 
        ? { ...player, hasSeenCard: true }
        : player
    ));
  };

  const handleNextPlayer = () => {
    setIsFlipped(false);
    setShowRole(false);
    setTimer(15);
    
    if (currentPlayer < config.playerCount - 1) {
      setCurrentPlayer(prev => prev + 1);
    } else {
      setGamePhase("ended");
    }
  };

  const resetGame = () => {
    setCurrentPlayer(0);
    setIsFlipped(false);
    setShowRole(false);
    setTimer(15);
    setGamePhase("setup");
    setPlayers(prev => prev.map(player => ({ ...player, hasSeenCard: false })));
  };

  if (gamePhase === "setup") {
    return (
      <div className="min-h-screen p-6 flex items-center justify-center">
        <Card className="card-ornate w-full max-w-md">
          <CardContent className="p-8 text-center space-y-6">
            <Scale className="h-16 w-16 text-justice-gold mx-auto animate-pulse" />
            <h2 className="text-2xl font-bold">Preparing Trial...</h2>
            <p className="text-muted-foreground">
              Setting up cards for {config.playerCount} players
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (gamePhase === "ended") {
    return (
      <div className="min-h-screen p-6 flex items-center justify-center">
        <Card className="card-ornate w-full max-w-md">
          <CardContent className="p-8 text-center space-y-6">
            <Scale className="h-16 w-16 text-justice-gold mx-auto" />
            <h2 className="text-2xl font-bold">Trial Complete!</h2>
            <p className="text-muted-foreground">
              All players have seen their cards. The trial may now begin!
            </p>
            <div className="space-y-3">
              <Button variant="justice" onClick={resetGame} className="w-full">
                <RotateCcw className="h-4 w-4 mr-2" />
                Start New Trial
              </Button>
              <Button variant="trial" onClick={onBackToMenu} className="w-full">
                <Home className="h-4 w-4 mr-2" />
                Back to Menu
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const currentPlayerData = players[currentPlayer];
  const progressPercentage = ((currentPlayer + (isFlipped ? 1 : 0)) / config.playerCount) * 100;

  return (
    <div className="min-h-screen p-6">
      {/* Header */}
      <div className="text-center mb-8">
        <div className="flex items-center justify-center gap-3 mb-4">
          <Scale className="h-8 w-8 text-justice-gold" />
          <h1 className="text-3xl font-bold bg-gradient-to-r from-justice-gold to-justice-bronze bg-clip-text text-transparent">
            Admin Liar
          </h1>
        </div>
        <div className="flex items-center justify-center gap-4 text-muted-foreground">
          <span>Player {currentPlayer + 1} of {config.playerCount}</span>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="mb-8">
        <Progress value={progressPercentage} className="h-3" />
        <div className="flex justify-between text-sm text-muted-foreground mt-2">
          <span>Progress</span>
          <span>{Math.round(progressPercentage)}%</span>
        </div>
      </div>

      {/* Main Card Area */}
      <div className="flex items-center justify-center mb-8">
        <div className="card-flip w-80 h-96 mx-auto">
          <div className={`card-flip-inner w-full h-full relative ${isFlipped ? 'rotate-y-180' : ''}`}>
            {/* Card Front */}
            <Card className={`card-face card-ornate absolute inset-0 ${isFlipped ? 'invisible' : 'visible'}`}>
              <CardContent className="h-full flex items-center justify-center p-8">
                <div className="text-center space-y-6">
                  <Scale className="h-20 w-20 text-justice-gold mx-auto" />
                  <h2 className="text-2xl font-bold">Player {currentPlayer + 1}</h2>
                  <p className="text-muted-foreground">
                    Click to reveal your role
                  </p>
                  <Button
                    variant="justice"
                    size="lg"
                    onClick={handleRevealCard}
                    className="animate-justice-glow"
                  >
                    Reveal My Card
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Card Back */}
            <Card className={`card-face card-back card-ornate absolute inset-0 ${isFlipped ? 'visible' : 'invisible'}`}>
              <CardContent className="h-full flex items-center justify-center p-8">
                <div className="text-center space-y-6">
                  {currentPlayerData?.isLiar ? (
                    <>
                      <img 
                        src={liarCardImage} 
                        alt="Liar Card" 
                        className="w-48 h-64 object-cover rounded-lg mx-auto shadow-lg"
                      />
                      <h2 className="text-2xl font-bold text-destructive">LIAR</h2>
                      <p className="text-lg">
                        You don't know the secret word.
                      </p>
                      <p className="text-muted-foreground text-sm">
                        Blend in and discover what it is!
                      </p>
                    </>
                  ) : (
                    <>
                      <img
                        src={cardImageMap[config.secretWord] || avDiceyCard}
                        alt={`${config.secretWord} Card`}
                        className="w-48 h-64 object-cover rounded-lg mx-auto shadow-lg"
                      />
                      <h2 className="text-2xl font-bold bg-gradient-to-r from-justice-gold to-justice-bronze bg-clip-text text-transparent">
                        {config.secretWord}
                      </h2>
                      <p className="text-lg">
                        You know the secret word.
                      </p>
                      <p className="text-muted-foreground text-sm">
                        Help find the liars!
                      </p>
                    </>
                  )}
                  
                  {/* Timer and Done Button */}
                  <div className="space-y-3">
                    <div className="text-lg font-semibold">
                      Time remaining: {timer}s
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Remember your role, then pass to the next player
                    </p>
                    <Button
                      variant="trial"
                      onClick={handleNextPlayer}
                      size="lg"
                      className="w-full"
                    >
                      Next Player
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Game Controls */}
      <div className="flex justify-center gap-4">
        <Button variant="gavel" onClick={resetGame}>
          <RotateCcw className="h-4 w-4 mr-2" />
          Reset
        </Button>
        <Button variant="secondary" onClick={onBackToMenu}>
          <Home className="h-4 w-4 mr-2" />
          Menu
        </Button>
      </div>
    </div>
  );
}