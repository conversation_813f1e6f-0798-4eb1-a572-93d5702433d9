import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Scale, ArrowRight, RotateCcw, Home } from "lucide-react";
import { GameConfig } from "./GameMenu";
import liarCardImage from "@/assets/liar-card.jpg";
import manipulatorCardImage from "@/assets/manipulator-card.jpg";

// Import all legal term card images
import avDiceyCard from "@/assets/av-dicey-card.jpg";
import naturalJusticeCard from "@/assets/natural-justice-card.jpg";
import parliamentarySovereigntyCard from "@/assets/parliamitaray-soverenity-card.jpg";
import delegatedLegislationCard from "@/assets/delegated-legislation-card.jpg";
import inReDelhiLawsActCard from "@/assets/inre-delhi-law-act-case-card.jpg";
import divineOriginTheoryCard from "@/assets/divine-origin-theory-card.jpg";
import droitAdministratifCard from "@/assets/droit-administratif-card.jpg";
import administrativeTribunalsCard from "@/assets/tribiunals-card.jpg";
import quasiJudicialFunctionsCard from "@/assets/admininitrative-law-card.jpg";
import discretionCard from "@/assets/discretion-card.jpg";
import nemoJudexCard from "@/assets/nemo-judex-in-causa-sua-card.jpg";

interface GamePlayProps {
  config: GameConfig;
  onBackToMenu: () => void;
}

interface PlayerCard {
  id: number;
  isLiar: boolean;
  isManipulator: boolean;
  hasSeenCard: boolean;
}

// Mapping of legal terms to their card images
const cardImageMap: Record<string, string> = {
  "AV DICEY": avDiceyCard,
  "NATURAL JUSTICE": naturalJusticeCard,
  "PARLIAMENTARY SOVEREIGNTY": parliamentarySovereigntyCard,
  "DELEGATED LEGISLATION": delegatedLegislationCard,
  "IN RE DELHI LAWS ACT": inReDelhiLawsActCard,
  "DIVINE ORIGIN THEORY": divineOriginTheoryCard,
  "DROIT ADMINISTRATIF": droitAdministratifCard,
  "ADMINISTRATIVE TRIBUNALS": administrativeTribunalsCard,
  "QUASI JUDICIAL FUNCTIONS": quasiJudicialFunctionsCard,
  "DISCRETION": discretionCard,
  "NEMO JUDEX IN CAUSA SUA": nemoJudexCard,
};

export function GamePlay({ config, onBackToMenu }: GamePlayProps) {
  const [players, setPlayers] = useState<PlayerCard[]>([]);
  const [currentPlayer, setCurrentPlayer] = useState(0);
  const [isFlipped, setIsFlipped] = useState(false);
  const [timer, setTimer] = useState(10);
  const [gamePhase, setGamePhase] = useState<"setup" | "playing" | "ended">("setup");
  const [showRole, setShowRole] = useState(false);

  useEffect(() => {
    // Initialize players
    const newPlayers: PlayerCard[] = [];
    const liarIndices = new Set<number>();
    let manipulatorIndex = -1;

    // Randomly select liars
    while (liarIndices.size < config.liarCount) {
      liarIndices.add(Math.floor(Math.random() * config.playerCount));
    }

    // If manipulator mode is enabled, randomly select one player to be the manipulator
    if (config.manipulatorMode) {
      // Manipulator should not be a liar
      const nonLiarIndices = Array.from({length: config.playerCount}, (_, i) => i)
        .filter(i => !liarIndices.has(i));
      manipulatorIndex = nonLiarIndices[Math.floor(Math.random() * nonLiarIndices.length)];
    }

    for (let i = 0; i < config.playerCount; i++) {
      newPlayers.push({
        id: i + 1,
        isLiar: liarIndices.has(i),
        isManipulator: i === manipulatorIndex,
        hasSeenCard: false,
      });
    }

    setPlayers(newPlayers);
    setGamePhase("playing");
  }, [config]);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (gamePhase === "playing" && showRole && timer > 0) {
      interval = setInterval(() => {
        setTimer((prev) => prev - 1);
      }, 1000);
    } else if (timer === 0 && showRole) {
      handleNextPlayer();
    }
    
    return () => clearInterval(interval);
  }, [gamePhase, showRole, timer]);

  const handleRevealCard = () => {
    // Add a slight delay for better animation effect
    setTimeout(() => {
      setIsFlipped(true);
      setShowRole(true);
      setTimer(15);
    }, 100);

    // Mark current player as having seen their card
    setPlayers(prev => prev.map((player, index) =>
      index === currentPlayer
        ? { ...player, hasSeenCard: true }
        : player
    ));
  };

  const handleNextPlayer = () => {
    setIsFlipped(false);
    setShowRole(false);
    setTimer(15);
    
    if (currentPlayer < config.playerCount - 1) {
      setCurrentPlayer(prev => prev + 1);
    } else {
      setGamePhase("ended");
    }
  };

  const resetGame = () => {
    setCurrentPlayer(0);
    setIsFlipped(false);
    setShowRole(false);
    setTimer(15);
    setGamePhase("setup");
    setPlayers(prev => prev.map(player => ({
      ...player,
      hasSeenCard: false,
      isLiar: false,
      isManipulator: false
    })));
  };

  if (gamePhase === "setup") {
    return (
      <div className="min-h-screen p-6 flex items-center justify-center">
        <Card className="card-ornate w-full max-w-md">
          <CardContent className="p-8 text-center space-y-6">
            <Scale className="h-16 w-16 text-justice-gold mx-auto animate-pulse" />
            <h2 className="text-2xl font-bold">Preparing Trial...</h2>
            <p className="text-muted-foreground">
              Setting up cards for {config.playerCount} players
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (gamePhase === "ended") {
    return (
      <div className="min-h-screen p-6 flex items-center justify-center">
        <Card className="card-ornate w-full max-w-md">
          <CardContent className="p-8 text-center space-y-6">
            <Scale className="h-16 w-16 text-justice-gold mx-auto" />
            <h2 className="text-2xl font-bold">Trial Complete!</h2>
            <p className="text-muted-foreground">
              All players have seen their cards. The trial may now begin!
            </p>
            <div className="space-y-3">
              <Button variant="justice" onClick={resetGame} className="w-full">
                <RotateCcw className="h-4 w-4 mr-2" />
                Start New Trial
              </Button>
              <Button variant="trial" onClick={onBackToMenu} className="w-full">
                <Home className="h-4 w-4 mr-2" />
                Back to Menu
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const currentPlayerData = players[currentPlayer];
  const progressPercentage = ((currentPlayer + (isFlipped ? 1 : 0)) / config.playerCount) * 100;

  // Get liar player numbers for manipulator
  const liarPlayerNumbers = players
    .filter(player => player.isLiar)
    .map(player => player.id)
    .sort((a, b) => a - b);

  return (
    <div className="min-h-screen p-6">
      {/* Header */}
      <div className="text-center mb-8">
        <div className="flex items-center justify-center gap-3 mb-4">
          <Scale className="h-8 w-8 text-justice-gold" />
          <h1 className="text-3xl font-bold bg-gradient-to-r from-justice-gold to-justice-bronze bg-clip-text text-transparent">
            Admin Liar
          </h1>
        </div>
        <div className="flex items-center justify-center gap-4 text-muted-foreground">
          <span>Player {currentPlayer + 1} of {config.playerCount}</span>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="mb-8">
        <Progress value={progressPercentage} className="h-3" />
        <div className="flex justify-between text-sm text-muted-foreground mt-2">
          <span>Progress</span>
          <span>{Math.round(progressPercentage)}%</span>
        </div>
      </div>

      {/* Main Card Area */}
      <div className="flex items-center justify-center mb-8">
        <div className="card-flip w-80 h-96 mx-auto">
          <div className={`card-flip-inner w-full h-full ${isFlipped ? 'flipped' : ''}`}>
            {/* Card Front */}
            <Card className="card-face card-ornate">
              <CardContent className="h-full flex items-center justify-center p-8">
                <div className="text-center space-y-6">
                  <Scale className="h-20 w-20 text-justice-gold mx-auto animate-pulse" />
                  <h2 className="text-2xl font-bold">Player {currentPlayer + 1}</h2>
                  <p className="text-muted-foreground">
                    Click to reveal your role
                  </p>
                  <Button
                    variant="justice"
                    size="lg"
                    onClick={handleRevealCard}
                    className="animate-justice-glow hover:scale-105 transition-transform"
                    disabled={isFlipped}
                  >
                    Reveal My Card
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Card Back */}
            <Card className={`card-face card-back card-ornate ${showRole ? 'card-revealed-glow' : ''}`}>
              <CardContent className="h-full flex items-center justify-center p-4">
                <div className="text-center space-y-4">
                  {currentPlayerData?.isLiar ? (
                    <>
                      <div className={`${showRole ? 'card-image-reveal' : ''}`}>
                        <img
                          src={liarCardImage}
                          alt="Liar Card"
                          className="w-40 h-56 object-cover rounded-lg mx-auto shadow-2xl border-2 border-destructive/20"
                        />
                      </div>
                      <div className={`space-y-2 ${showRole ? 'card-image-reveal' : ''}`} style={{animationDelay: '0.2s'}}>
                        <h2 className="text-xl font-bold text-destructive animate-pulse">LIAR</h2>
                        <p className="text-sm font-medium">
                          You don't know the secret word.
                        </p>
                        <p className="text-muted-foreground text-xs">
                          Blend in and discover what it is!
                        </p>
                      </div>
                    </>
                  ) : currentPlayerData?.isManipulator ? (
                    <>
                      <div className={`${showRole ? 'card-image-reveal' : ''}`}>
                        <img
                          src={manipulatorCardImage}
                          alt="Manipulator Card"
                          className="w-40 h-56 object-cover rounded-lg mx-auto shadow-2xl border-2 border-purple-500/30"
                        />
                      </div>
                      <div className={`space-y-2 ${showRole ? 'card-image-reveal' : ''}`} style={{animationDelay: '0.2s'}}>
                        <h2 className="text-lg font-bold bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent">
                          MANIPULATOR
                        </h2>
                        <p className="text-sm font-medium">
                          Secret word: <span className="text-justice-gold font-bold">{config.secretWord}</span>
                        </p>
                        <div className="text-xs space-y-1">
                          <p className="text-muted-foreground">Liars are:</p>
                          <p className="text-destructive font-bold">
                            Player{liarPlayerNumbers.length > 1 ? 's' : ''} {liarPlayerNumbers.join(', ')}
                          </p>
                          <p className="text-muted-foreground">Protect them subtly!</p>
                        </div>
                      </div>
                    </>
                  ) : (
                    <>
                      <div className={`${showRole ? 'card-image-reveal' : ''}`}>
                        <img
                          src={cardImageMap[config.secretWord] || avDiceyCard}
                          alt={`${config.secretWord} Card`}
                          className="w-40 h-56 object-cover rounded-lg mx-auto shadow-2xl border-2 border-justice-gold/30"
                        />
                      </div>
                      <div className={`space-y-2 ${showRole ? 'card-image-reveal' : ''}`} style={{animationDelay: '0.2s'}}>
                        <h2 className="text-lg font-bold bg-gradient-to-r from-justice-gold to-justice-bronze bg-clip-text text-transparent">
                          {config.secretWord}
                        </h2>
                        <p className="text-sm font-medium">
                          You know the secret word.
                        </p>
                        <p className="text-muted-foreground text-xs">
                          Help find the liars!
                        </p>
                      </div>
                    </>
                  )}

                  {/* Timer and Done Button */}
                  <div className={`space-y-3 ${showRole ? 'card-image-reveal' : ''}`} style={{animationDelay: '0.4s'}}>
                    <div className="text-base font-semibold">
                      <span className={`${timer <= 5 ? 'text-destructive animate-pulse' : 'text-justice-gold'}`}>
                        Time remaining: {timer}s
                      </span>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Remember your role, then pass to the next player
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Next Player Button - Prominently placed and separated */}
      {showRole && (
        <div className="flex justify-center mb-12">
          <Button
            variant="trial"
            onClick={handleNextPlayer}
            size="xl"
            className="px-12 py-4 text-lg font-semibold hover:scale-105 transition-transform animate-justice-glow"
          >
            Next Player
            <ArrowRight className="h-5 w-5 ml-3" />
          </Button>
        </div>
      )}

      {/* Game Controls - Separated and smaller */}
      <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2">
        <div className="flex gap-3 bg-background/80 backdrop-blur-sm rounded-full px-4 py-2 border border-border/50">
          <Button variant="outline" size="sm" onClick={resetGame} className="text-xs">
            <RotateCcw className="h-3 w-3 mr-1" />
            Reset
          </Button>
          <Button variant="outline" size="sm" onClick={onBackToMenu} className="text-xs">
            <Home className="h-3 w-3 mr-1" />
            Menu
          </Button>
        </div>
      </div>
    </div>
  );
}