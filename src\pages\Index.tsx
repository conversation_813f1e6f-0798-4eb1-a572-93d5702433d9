import { useState } from "react";
import { GameMenu, GameConfig } from "@/components/GameMenu";
import { GamePlay } from "@/components/GamePlay";

const Index = () => {
  const [gameConfig, setGameConfig] = useState<GameConfig | null>(null);

  const handleStartGame = (config: GameConfig) => {
    setGameConfig(config);
  };

  const handleBackToMenu = () => {
    setGameConfig(null);
  };

  return (
    <div className="min-h-screen bg-background">
      {gameConfig ? (
        <GamePlay config={gameConfig} onBackToMenu={handleBackToMenu} />
      ) : (
        <GameMenu onStartGame={handleStartGame} />
      )}
    </div>
  );
};

export default Index;
